<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chic.quality.domain.database.mapper.RuleMapper">

    <select id="selectByPage" resultType="com.chic.quality.apis.model.vo.QueryRuleVo">
        <if test="ruleName != null and ruleName != ''">
            <bind name="ruleName" value="'%' + ruleName + '%'" />
        </if>
        SELECT
            r.id,
            r.ruleset_id as rulesetId,
            r.rule_name as ruleName,
            r.rule_type as ruleType,
            r.template_id as templateId,
            t.template_name as templateName,
            r.rule_strength as ruleStrength,
            r.enable as enable,
            r.`filter` as filter,
            r.create_by as createBy,
            r.update_by as updateBy,
            r.create_by_name as createByName,
            r.update_by_name as updateByName,
            r.create_at as createAt,
            r.update_at as updateAt,
            ds.id as dataSetId,
            ds.name as dataSetName,r.trial_result as trialResult
            ,r.remark as remark
            ,r.watch_type as watchType
            ,r.exception_archive as exceptionArchive
            ,r.archive_mode as archiveMode
            ,r.score_type as scoreType
            ,r.quality_score as qualityScore
            ,r.config_method as configMethod
            ,r.mantis_project_id as mantisProjectId
            ,r.mantis_handler_id as mantisHandlerId
        FROM data_quality_rule r
        left join data_quality_rule_template t on r.template_id = t.id
        left join data_set ds on r.watch_id = ds.id
        <where>
            r.del_flag = '0' and r.ruleset_id = #{rulesetId}
            <if test="ruleName != null and ruleName != ''">
                AND r.rule_name like #{ruleName}
            </if>
        </where>
        order by r.create_at
    </select>

</mapper>
