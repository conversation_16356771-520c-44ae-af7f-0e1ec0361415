package com.chic.quality.domain.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chic.commons.base.PageData;
import com.chic.commons.exception.ApiException;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.quality.apis.model.dto.DataSetDTO;
import com.chic.quality.apis.model.vo.DataSetVo;
import com.chic.quality.apis.model.vo.SaveDataSetVo;
import com.chic.quality.apis.model.vo.UpdateDataSetVo;
import com.chic.quality.apis.protocol.mapping.DataSetMapping;
import com.chic.quality.domain.database.entity.DataSet;
import com.chic.quality.domain.database.entity.DataSetMeta;
import com.chic.quality.domain.database.entity.DataSource;
import com.chic.quality.domain.database.entity.DataSetGroups;
import com.chic.quality.domain.database.entity.Rule;
import com.chic.quality.domain.database.mapper.DataSetMapper;
import com.chic.quality.domain.service.DataSetMetaService;
import com.chic.quality.domain.service.DataSetService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chic.quality.domain.service.DataSourceService;
import com.chic.quality.domain.service.DataSetGroupsService;
import com.chic.quality.domain.service.SparkEngineService;
import com.chic.quality.domain.service.RuleService;
import com.chic.quality.infrastructure.general.util.MybatisApiUtils;
import com.chic.quality.infrastructure.general.util.SqlDateParser;
import com.chic.quality.infrastructure.general.util.SqlParserUtil;
import com.chic.quality.infrastructure.general.util.TreeNode;
import com.chic.quality.infrastructure.metadata.ColumnMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.sql.SqlNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.Objects;

/**
 * <p>
 * 数据集信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Service
public class DataSetServiceImpl extends ServiceImpl<DataSetMapper, DataSet> implements DataSetService {
    @Autowired
    private DataSetMapping dataSetMapping;
    @Autowired
    private DataSetMapper dataSetMapper;
    @Autowired
    private SparkEngineService sparkEngineService;
    @Autowired
    private DataSourceService dataSourceService;
    @Autowired
    private DataSetMetaService dataSetMetaService;

    @Autowired
    @Lazy
    private RuleService ruleService;


    @Override
    public boolean checkSql(Long dataSourceId, String sql) {
        log.info(">>> 开始校验SQL语法输入SQL为：\n{}", sql);
        DataSource dataSource = dataSourceService.getById(dataSourceId);
        String executeSql = SqlParserUtil.parseDatePlaceholders(sql, LocalDate.now(),dataSource.getType());
        log.info(">>> 开始执行SQL：\n{}", executeSql);
        return sparkEngineService.checkSql(dataSource, executeSql);
    }

    @Override
    public List<ColumnMetadata> parseSql(Long dataSourceId, String sql) {
        DataSource dataSource = dataSourceService.getById(dataSourceId);
        //SqlNode sqlNode = SqlParserUtil.getSqlNode(sql);
        SqlNode sqlNode = SqlParserUtil.parseDatePlaceholders(SqlParserUtil.getSqlNode(sql), LocalDate.now());

        List<ColumnMetadata> columnMetadataList = SqlParserUtil.extractColumnMetaData(sqlNode);
        LinkedHashMap<String, Integer> columnType = sparkEngineService.queryColumnType(dataSource,
                SqlParserUtil.sqlNodeToString(sqlNode,dataSource.getType()));

        SqlParserUtil.setColumnType(columnMetadataList, columnType);
        return columnMetadataList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(SaveDataSetVo vo) {
        this.checkName(vo.getName());
        DataSet po = dataSetMapping.toPo(vo);
        this.save(po);
        return po.getId();
    }

    private void checkName(String name) {
        LambdaQueryWrapper<DataSet> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DataSet::getName, name);
        if(this.count(wrapper)>0){
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(),"数据集名称重复" ));
        }
    }
    private void checkName(Long id,String name) {
        LambdaQueryWrapper<DataSet> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DataSet::getName, name);
        wrapper.ne(DataSet::getId,id);
        if(this.count(wrapper)>0){
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(),"数据集名称重复" ));
        }
    }

    private void saveColumnMetaData(Long dataSetId ,List<ColumnMetadata> columnMetadataList) {
        if(!CollectionUtils.isEmpty(columnMetadataList)){
            List<DataSetMeta> list = new ArrayList<>();
            for(ColumnMetadata columnMetadata : columnMetadataList){
                DataSetMeta dataSetMeta = dataSetMapping.toPo(columnMetadata);
                dataSetMeta.setDataSetId(dataSetId);
                list.add(dataSetMeta);
            }
            dataSetMetaService.saveBatch(list);
        }
    }

    @Override
    public boolean update(UpdateDataSetVo vo) {
        checkName(vo.getId(), vo.getName());
        DataSet po = dataSetMapping.toPo(vo);
        return this.updateById(po);
    }

    @Override
    public DataSetDTO selectDsAndCol(Long dataSetId, List<Long> colMetaDataIds) {
        DataSet dataSet = getById(dataSetId);
        DataSetDTO dto = dataSetMapping.toDto(dataSet);
        Long dataSourceId = dto.getDataSourceId();
        dto.setDataSource(dataSourceService.getById(dataSourceId));
        dto.setDataSetMetaList(dataSetMetaService.listByIds(colMetaDataIds));
        return dto;
    }

    @Override
    public DataSetDTO selectDsAndCol(Long dataSetId, Long colMetaDataId) {
        DataSet dataSet = getById(dataSetId);
        DataSetDTO dto = dataSetMapping.toDto(dataSet);
        Long dataSourceId = dto.getDataSourceId();
        dto.setDataSource(dataSourceService.getById(dataSourceId));
        //dto.setDataSetMeta(dataSetMetaService.getById(colMetaDataId));
        return dto;
    }

    @Override
    public DataSetDTO selectDsAndDataSet(Long dataSetId) {
        DataSet dataSet = getById(dataSetId);
        DataSetDTO dto = dataSetMapping.toDto(dataSet);
        Long dataSourceId = dto.getDataSourceId();
        dto.setDataSource(dataSourceService.getById(dataSourceId));
        return dto;
    }

    @Override
    public int countByGroupId(Long groupId) {
        LambdaQueryWrapper<DataSet> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DataSet::getGroupId, groupId);
        return this.count(wrapper);
    }

    @Override
    public List<TreeNode> selectByGroupId(Long groupId) {
        LambdaQueryWrapper<DataSet> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DataSet::getGroupId, groupId);
        wrapper.orderByDesc(DataSet::getUpdateTime);
        List<DataSet> list = list(wrapper);
        return dataSetMapping.toTreeNodes(list);
    }

    @Override
    public List<TreeNode> selectTreeByName(String dataSetName) {
        LambdaQueryWrapper<DataSet> wrapper = Wrappers.lambdaQuery();
        wrapper.like(DataSet::getName, dataSetName);
        wrapper.orderByDesc(DataSet::getUpdateTime);
        List<DataSet> list = list(wrapper);
        return dataSetMapping.toTreeNodes(list);
    }

    @Override
    public PageData<DataSetVo> selectByKeywords(String dataSetName, String projectName, Long dataSourceId) {
        IPage pageParam = MybatisApiUtils.getPageParam();
        IPage<DataSetVo> page = dataSetMapper.selectPage(pageParam, dataSetName, projectName, dataSourceId);
        return PageData.convert(page);
    }

    @Override
    public DataSetVo selectById(Long dataSetId) {
        DataSetVo vo = dataSetMapper.selectVoById(dataSetId);
        List<DataSetMeta> dataSetMetaList = dataSetMetaService.listByDataSetId(dataSetId);
        vo.setDataSetMetaList(dataSetMetaList);
        return vo;
    }

    @Override
    public boolean saveSql(Long dataSetId, String sql) {
        DataSet dataSet = getById(dataSetId);
        updateDataSetMeta(dataSetId, sql, dataSet);
        return updateById(new DataSet(dataSet.getId(),sql));
    }

    private void updateDataSetMeta(Long dataSetId, String sql, DataSet dataSet) {
        List<ColumnMetadata> columnMetadataList = parseSql(dataSet.getDataSourceId(), sql);
        if (CollectionUtils.isEmpty(columnMetadataList)) {
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "解析SQL失败，请检查SQL语法是否正确"));
        }
    
        List<DataSetMeta> newDataSetMetas = dataSetMapping.columnMetaDataToPo(dataSetId, columnMetadataList);
        List<DataSetMeta> oldDataSetMetas = dataSetMetaService.listByDataSetId(dataSetId);

        // Create maps for quick lookup by keyHash
        Map<String, DataSetMeta> newMetaMap = newDataSetMetas.stream()
            .collect(Collectors.toMap(DataSetMeta::getKeyHash, Function.identity()));
        Map<String, DataSetMeta> oldMetaMap = oldDataSetMetas.stream()
            .collect(Collectors.toMap(DataSetMeta::getKeyHash, Function.identity()));

        // Determine which metas to insert, update, or delete
        List<DataSetMeta> metasToInsert = new ArrayList<>();
        List<DataSetMeta> metasToUpdate = new ArrayList<>();
        List<DataSetMeta> metasToDelete = new ArrayList<>();

        for (DataSetMeta newMeta : newDataSetMetas) {
            if (!oldMetaMap.containsKey(newMeta.getKeyHash())) {
                metasToInsert.add(newMeta);
            } else {
                newMeta.setId(oldMetaMap.get(newMeta.getKeyHash()).getId());
                metasToUpdate.add(newMeta);
            }
        }

        for (DataSetMeta oldMeta : oldDataSetMetas) {
            if (!newMetaMap.containsKey(oldMeta.getKeyHash())) {
                metasToDelete.add(oldMeta);
            }
        }
        // Perform database operations
        if (!metasToInsert.isEmpty()) {
            dataSetMetaService.saveBatch(metasToInsert);
        }
        if (!metasToUpdate.isEmpty()) {
            dataSetMetaService.updateBatchById(metasToUpdate);
        }
        if (!metasToDelete.isEmpty()) {
            dataSetMetaService.removeBatchByIds(metasToDelete.stream().map(DataSetMeta::getId).collect(Collectors.toList()));
        }
    }

    @Override
    public List<Map<String,Object>> preview(Long dataSetId, String sql, String bizDate) {
        DataSet dataSet = getById(dataSetId);
        DataSource dataSource = dataSourceService.getById(dataSet.getDataSourceId());

        LocalDate baseDate = LocalDate.now();
        if (bizDate != null) {
            baseDate = LocalDate.parse(bizDate, DatePattern.NORM_DATE_FORMATTER);
        }
        String executeSql = SqlParserUtil.buildPreviewSql(sql,baseDate,dataSource.getType());
        log.info("预览SQL为：\n{}",executeSql);
        return sparkEngineService.queryForList(dataSource, executeSql);
    }



    @Override
    public List<DataSet> selectByName(Long dataSetId,String name) {
        if(dataSetId != null){
            List<DataSet> list = new ArrayList<>();
            list.add(getById(dataSetId));
            return list;
        }
        LambdaQueryWrapper<DataSet> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByDesc(DataSet::getUpdateTime);
        if(StrUtil.isBlank(name)){
            wrapper.last("limit 20");
        }else{
            wrapper.like(DataSet::getName, name);
        }
        return list(wrapper);
    }

    @Override
    public Boolean moveDataSet(Long dataSetId, Long targetGroupId) {
        // 1. 验证数据集存在
        DataSet dataSet = this.getById(dataSetId);
        if (dataSet == null) {
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "数据集不存在"));
        }

        // 3. 验证不能移动到当前所在目录
        if (Objects.equals(dataSet.getGroupId(), targetGroupId)) {
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "不能移动到当前所在目录"));
        }
        
        // 4. 仅更新数据集分组ID字段        
        return dataSetMapper.updateById( new DataSet(dataSetId, targetGroupId)) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchMoveDataSet(List<Long> dataSetIds, Long targetGroupId) {
        if (CollectionUtils.isEmpty(dataSetIds)) {
            return true; // 没有数据集需要移动，直接返回成功
        }

        // 2. 批量查询数据集
        List<DataSet> dataSets = this.listByIds(dataSetIds);
        if (dataSets.size() != dataSetIds.size()) {
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "部分数据集不存在"));
        }

        // 3. 验证不能将数据集移动到当前所在目录
        for (DataSet dataSet : dataSets) {
            if (Objects.equals(dataSet.getGroupId(), targetGroupId)) {
                throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), 
                    "数据集" + dataSet.getName() + "已在目标目录中，不能移动到当前所在目录"));
            }
        }

        // 4. 批量更新数据集分组ID（仅更新groupId字段）
        List<DataSet> updateEntities = new ArrayList<>();
        for (DataSet dataSet : dataSets) {
            updateEntities.add(new DataSet(dataSet.getId(), targetGroupId));
        }
        
        return updateBatchById(updateEntities);
    }

    @Override
    public Boolean checkIsUsedByRule(Long dataSetId) {
        // 查询是否存在规则的watchId或compareDataSetId与当前数据集ID相同
        LambdaQueryWrapper<Rule> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Rule::getWatchId, dataSetId)
                .or()
                .eq(Rule::getCompareDataSetId, dataSetId);
        
        return ruleService.count(wrapper) > 0;
    }
}
