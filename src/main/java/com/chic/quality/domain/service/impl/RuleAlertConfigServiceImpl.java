package com.chic.quality.domain.service.impl;

import com.alibaba.excel.ExcelWriter;
import com.chic.commons.util.StringUtils;
import com.chic.quality.apis.model.dto.MantisIssueCreateDTO;
import com.chic.quality.apis.model.dto.RuleDTO;
import com.chic.quality.apis.model.dto.UserDTO;
import com.chic.quality.apis.model.vo.RulesetVo;
import com.chic.quality.domain.database.entity.RuleAlertConfig;
import com.chic.quality.domain.database.mapper.RuleAlertConfigMapper;
import com.chic.quality.domain.service.RuleAlertConfigService;
import com.chic.quality.infrastructure.general.converter.TimestampConverter;
import com.chic.quality.infrastructure.general.util.EmailUtil;
import com.chic.quality.infrastructure.general.util.MsgUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.ArrayList;

import java.util.stream.Collectors;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chic.quality.domain.database.entity.Rule;
import com.chic.quality.domain.database.entity.RulesetValidateLog;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.io.InputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import java.util.Map;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;

// 添加Minio相关的import
import com.chic.minio.MinioService;

// 添加HTTP客户端相关的import
import okhttp3.*;
import java.util.concurrent.TimeUnit;


/**
 * <p>
 * 规则告警配置信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
@Service
@Slf4j
public class RuleAlertConfigServiceImpl extends ServiceImpl<RuleAlertConfigMapper, RuleAlertConfig> implements RuleAlertConfigService {
    @Value("${custom.excel.tempDir}")
    private String tempDirPath;

    @Value("${spring.minio.bucket}")
    private String bucket;

    @Value("${spring.minio.path}")
    private String minioPath;

    @Value("${mantis.url}")
    private String mantisUrl;

    @Value("${mantis.createIssues}")
    private String createIssues;

    @Value("${mantis.authorization}")
    private String mantisAuthorization;


    @Autowired
    private MinioService minioService;




    
    @Override
    public List<RuleAlertConfig> listByRulesetId(Long rulesetId) {
        LambdaQueryWrapper<RuleAlertConfig> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(RuleAlertConfig::getRulesetId, rulesetId);
        queryWrapper.orderByDesc(RuleAlertConfig::getUpdateAt);
        return list(queryWrapper);
    }

    @Override
    public void handleAlerts(List<RuleAlertConfig> alertConfigs, List<RuleDTO> failedRules, RulesetVo ruleset, RulesetValidateLog rulesetValidateLog) {
        log.info("开始处理告警信息");
        for (RuleAlertConfig alertConfig : alertConfigs) {
            List<RuleDTO> rulesToAlert = getRulesToAlert(alertConfig, failedRules);
            if (!rulesToAlert.isEmpty()) {
                // 构建告警消息
                String message = buildAlertMessage(rulesToAlert,rulesetValidateLog);
                // 发送告警
                sendAlert(alertConfig, message,rulesToAlert,rulesetValidateLog);
            }
        }
    }

    private List<RuleDTO> getRulesToAlert(RuleAlertConfig alertConfig, List<RuleDTO> failedRules) {
        List<RuleDTO> rulesToAlert = new ArrayList<>();
        String coverageType = alertConfig.getCoverageType();
        switch (coverageType) {
            case "all":
                rulesToAlert.addAll(failedRules);
                break;
            case "strong":
                for (RuleDTO rule : failedRules) {
                    if ("STRONG".equals(rule.getRuleStrength())) {
                        rulesToAlert.add(rule);
                    }
                }
                break;
            case "weak":
                for (RuleDTO rule : failedRules) {
                    if ("WEAK".equals(rule.getRuleStrength())) {
                        rulesToAlert.add(rule);
                    }
                }
                break;
            case "custom":
                List<Rule> rules = JSON.parseArray(alertConfig.getRuleCollection(), Rule.class);
                List<Long> ruleIds = rules.stream().map(Rule::getId).collect(Collectors.toList());
                for (RuleDTO rule : failedRules) {
                    if (ruleIds.contains(rule.getId())) {
                        rulesToAlert.add(rule);
                    }
                }
                break;
            default:
                break;
        }
        return rulesToAlert;
    }

    private String buildAlertMessage(List<RuleDTO> rulesToAlert, RulesetValidateLog validateLog) {
        StringBuilder message = new StringBuilder();
        message.append("<数据质量告警通知>");
        message.append(String.format("%s | %s | %s\n", validateLog.getProjectName(), validateLog.getRulesetName(),validateLog.getBatchNumber()));
        message.append("触发规则：\n");
        for (RuleDTO rule : rulesToAlert) {
            message.append("- ").append(rule.getAlertMsg()).append("\n");
        }
        message.append("请及时处理！详情登录邮箱或系统查看。\n");
        return message.toString();
    }

    private void sendAlert(RuleAlertConfig alertConfig, String message,List<RuleDTO> rulesToAlert,RulesetValidateLog rulesetValidateLog) {
        // 获取告警接收人
        List<UserDTO> receivers = JSON.parseArray(alertConfig.getAlertReceivers(), UserDTO.class);
        // 获取告警方式
        String alertMethods = alertConfig.getAlertMethods();

        // 根据告警方式发送告警（示例代码，需要替换为实际告警发送逻辑）
        if (alertMethods.contains("SMS")) {
            List<String> mobiles = receivers.stream().map(UserDTO::getMobile).collect(Collectors.toList());
            sendSms(mobiles, message);
        }
        if (alertMethods.contains("EMAIL")) {
            // 发送邮件，如果有附件则附加
            sendEmail(receivers.stream().map(UserDTO::getEmail).collect(Collectors.toList()), message, rulesToAlert,rulesetValidateLog);
        }
        // 对接mantis
        sendMantis(rulesToAlert);

    }

    private void sendMantis(List<RuleDTO> rulesToAlert) {
        for(RuleDTO rule : rulesToAlert) {
            // mantis 配置是否为空
            if(StringUtils.isNotBlank(rule.getMantisProjectId()) && StringUtils.isNotBlank(rule.getMantisHandlerId())){
                MantisIssueCreateDTO mantisIssueCreateDTO = new MantisIssueCreateDTO();
                mantisIssueCreateDTO.setProject(new MantisIssueCreateDTO.ProjectInfo(rule.getMantisProjectId()));
                mantisIssueCreateDTO.setCategory(new MantisIssueCreateDTO.CategoryInfo(74));
                mantisIssueCreateDTO.setHandler(new MantisIssueCreateDTO.HandlerInfo(rule.getMantisHandlerId()));
                mantisIssueCreateDTO.setSummary("[天才] "+rule.getRuleName()+" 触发告警");
    
                try {
                    // 获取规则的名称
                    String ruleName = rule.getRuleName();
                    // 将数据和sql写入Excel文件
                    String excelUrl = createAndUploadExcel(rule);
                    
                    // 设置工单描述信息
                    String description = rule.getAlertMsg();
                    if (StringUtils.isNotBlank(excelUrl)) {
                        description += "，详细数据文件：" + excelUrl;
                    }
                    if (StringUtils.isBlank(description)) {
                        description = "数据质量规则触发告警，请及时处理。";
                    }
                    mantisIssueCreateDTO.setDescription(description);
                    
                    log.info("规则 {} 的Excel文件已生成并上传，URL: {}", ruleName, excelUrl);

                    // 推送工单到mantis系统
                    sendMantisIssue(mantisIssueCreateDTO);
                    
                } catch (Exception e) {
                    log.error("规则 {} 生成Excel文件失败", rule.getRuleName(), e);
                }
            } else {
                log.debug("规则 {} 未配置Mantis信息，跳过工单创建", rule.getRuleName());
            }
        }
        
    }

    /**
     * 创建Excel文件并上传到Minio
     * @param rule 规则对象
     * @return 上传后的文件URL
     */
    private String createAndUploadExcel(RuleDTO rule) {
        try {
            // 创建临时目录
            File tempDir = new File(tempDirPath);
            if (!tempDir.exists()) {
                tempDir.mkdirs();
            }
            
            // 生成文件名：规则名称_时间戳
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String fileName = rule.getRuleName() + "_" + timestamp + ".xlsx";
            File excelFile = new File(tempDir, fileName);
            
            // 创建Excel文件
            ExcelWriter excelWriter = EasyExcel.write(excelFile)
                .registerConverter(new TimestampConverter())
                .build();
            
            try {
                // Sheet 1: 数据sheet
                if (rule.getRowData() != null && !rule.getRowData().isEmpty()) {
                    WriteSheet dataSheet = EasyExcel.writerSheet(0, "数据详情").build();
                    
                    // 准备表头和数据
                    Map<String, Object> firstRow = rule.getRowData().get(0);
                    List<String> headers = new ArrayList<>(firstRow.keySet());
                    
                    // 写入表头
                    List<List<String>> headList = new ArrayList<>();
                    for (String header : headers) {
                        List<String> head = new ArrayList<>();
                        head.add(header);
                        headList.add(head);
                    }
                    
                    // 写入数据
                    List<List<Object>> dataList = new ArrayList<>();
                    for (Map<String, Object> row : rule.getRowData()) {
                        List<Object> rowData = new ArrayList<>();
                        for (String header : headers) {
                            rowData.add(row.get(header));
                        }
                        dataList.add(rowData);
                    }
                    
                    WriteTable writeTable = new WriteTable();
                    writeTable.setHead(headList);
                    excelWriter.write(dataList, dataSheet, writeTable);
                }
                
                // Sheet 2: SQL sheet
                WriteSheet sqlSheet = EasyExcel.writerSheet(1, "执行SQL").build();
                
                // 设置表头
                List<List<String>> sqlHeadList = new ArrayList<>();
                List<String> head1 = new ArrayList<>();
                head1.add("规则名称");
                sqlHeadList.add(head1);
                List<String> head2 = new ArrayList<>();
                head2.add("SQL语句");
                sqlHeadList.add(head2);
                
                // 准备数据
                List<List<Object>> sqlDataList = new ArrayList<>();
                List<Object> rowData = new ArrayList<>();
                rowData.add(rule.getRuleName());
                rowData.add(rule.getRowDataSql());
                sqlDataList.add(rowData);
                
                // 写入SQL数据
                WriteTable sqlTable = new WriteTable();
                sqlTable.setHead(sqlHeadList);
                excelWriter.write(sqlDataList, sqlSheet, sqlTable);
                
            } finally {
                excelWriter.finish();
            }
            
            // 上传到Minio
            String uploadUrl = uploadToMinio(excelFile, fileName);
            
            // 清理临时文件
            if (excelFile.exists()) {
                excelFile.delete();
            }
            
            return uploadUrl;
            
        } catch (Exception e) {
            log.error("创建Excel文件失败", e);
            return null;
        }
    }

    /**
     * 上传文件到Minio
     * @param file 要上传的文件
     * @param fileName 文件名
     * @return 上传后的文件URL
     */
    private String uploadToMinio(File file, String fileName) {
        try {
            // 构建文件路径：/基础路径/年月/文件名
            String yearMonth = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
            String fullPath = minioPath + yearMonth + "/" + fileName;
            
            // 创建MultipartFile包装器
            MultipartFile multipartFile = new MultipartFile() {
                @Override
                public String getName() {
                    return fileName;
                }
                
                @Override
                public String getOriginalFilename() {
                    return fileName;
                }
                
                @Override
                public String getContentType() {
                    return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                }
                
                @Override
                public boolean isEmpty() {
                    return file.length() == 0;
                }
                
                @Override
                public long getSize() {
                    return file.length();
                }
                
                @Override
                public byte[] getBytes() throws IOException {
                    try (FileInputStream fis = new FileInputStream(file)) {
                        byte[] bytes = new byte[(int) file.length()];
                        fis.read(bytes);
                        return bytes;
                    }
                }
                
                @Override
                public InputStream getInputStream() throws IOException {
                    return new FileInputStream(file);
                }
                
                @Override
                public void transferTo(File dest) throws IOException, IllegalStateException {
                    // 实现文件转移
                }
            };
            
            String url = minioService.uploadBasics(bucket, multipartFile, null, fullPath);
            log.info("文件上传成功，URL: {}", url);
            return url;
            
        } catch (Exception e) {
            log.error("上传文件到Minio失败", e);
            return null;
        }
    }

    /**
     * 压缩文件为ZIP格式
     * @param sourceFile 源文件
     * @param zipFileName ZIP文件名
     * @return 压缩后的ZIP文件
     */
    private File compressFile(File sourceFile, String zipFileName) {
        if (sourceFile == null || !sourceFile.exists()) {
            log.warn("源文件不存在，无法进行压缩");
            return null;
        }

        try {
            // 创建ZIP文件
            File zipFile = new File(sourceFile.getParent(), zipFileName);

            try (FileOutputStream fos = new FileOutputStream(zipFile);
                 ZipOutputStream zos = new ZipOutputStream(fos);
                 FileInputStream fis = new FileInputStream(sourceFile)) {

                // 创建ZIP条目
                ZipEntry zipEntry = new ZipEntry(sourceFile.getName());
                zos.putNextEntry(zipEntry);

                // 将文件内容写入ZIP
                byte[] buffer = new byte[1024];
                int length;
                while ((length = fis.read(buffer)) > 0) {
                    zos.write(buffer, 0, length);
                }

                zos.closeEntry();
                log.info("文件压缩成功：{} -> {}", sourceFile.getName(), zipFile.getName());
                return zipFile;

            }
        } catch (IOException e) {
            log.error("文件压缩失败：{}", sourceFile.getName(), e);
            return null;
        }
    }

    private void sendSms(List<String> receivers, String message) {
        JSONObject param = new JSONObject();
        // 短信信息
        JSONObject json = new JSONObject();
        json.put("content", message);
        for(String phone : receivers) {
            param.put("mobile", phone);
            param.put("message", json.toJSONString());
            MsgUtil.sendSms(param.toJSONString());
        }
        // 发送短信告警（需要实现具体的发送逻辑）
        log.info("发送短信告警给{}，内容：{}", receivers, message);
    }

    private void sendEmail(List<String> receivers, String message, List<RuleDTO> rulesToAlert,RulesetValidateLog rulesetValidateLog) {
        try {
            File tempDir = new File(tempDirPath);
            if (!tempDir.exists()) {
                tempDir.mkdirs();
            }
          
            String date = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            
            File excelFile = new File(tempDir, rulesetValidateLog.getRulesetName()+"_"+date + ".xlsx");
            // 使用EasyExcel创建单个Excel文件，包含多个sheet
            try{
                // 注册自定义转换器，处理Timestamp类型
                ExcelWriter excelWriter = EasyExcel.write(excelFile)
                    .registerConverter(new TimestampConverter())
                    .build();
                
                // 为每个规则创建一个sheet
                int sheetNo = 0;
                for (RuleDTO rule : rulesToAlert) {
                    if (rule.getRowData() != null && !rule.getRowData().isEmpty()) {
                        // 创建以规则名称命名的sheet
                        String sheetName = rule.getRuleName() != null ? rule.getRuleName() : "Rule_" + rule.getId();
                        // 限制sheet名称长度，Excel限制为31个字符
                        if (sheetName.length() > 31) {
                            sheetName = sheetName.substring(0, 31);
                        }
                        
                        WriteSheet writeSheet = EasyExcel.writerSheet(sheetNo++, sheetName).build();
                        
                        // 准备表头和数据
                        if (!rule.getRowData().isEmpty()) {
                            Map<String, Object> firstRow = rule.getRowData().get(0);
                            List<String> headers = new ArrayList<>(firstRow.keySet());
                            
                            // 写入表头
                            List<List<String>> headList = new ArrayList<>();
                            for (String header : headers) {
                                List<String> head = new ArrayList<>();
                                head.add(header);
                                headList.add(head);
                            }
                            
                            // 写入数据
                            List<List<Object>> dataList = new ArrayList<>();
                            for (Map<String, Object> row : rule.getRowData()) {
                                List<Object> rowData = new ArrayList<>();
                                for (String header : headers) {
                                    rowData.add(row.get(header));
                                }
                                dataList.add(rowData);
                            }
                            
                            WriteTable writeTable = new WriteTable();
                            writeTable.setHead(headList);
                            excelWriter.write(dataList, writeSheet, writeTable);
                        }
                    }
                }
                
                // 添加一个额外的sheet页，存放规则的查询SQL
                WriteSheet sqlSheet = EasyExcel.writerSheet(sheetNo, "规则查询SQL").build();
                
                // 设置表头
                List<List<String>> sqlHeadList = new ArrayList<>();
                List<String> head1 = new ArrayList<>();
                head1.add("规则名称");
                sqlHeadList.add(head1);
                List<String> head2 = new ArrayList<>();
                head2.add("SQL语句");
                sqlHeadList.add(head2);
                
                // 准备数据
                List<List<Object>> sqlDataList = new ArrayList<>();
                for (RuleDTO rule : rulesToAlert) {
                    List<Object> rowData = new ArrayList<>();
                    rowData.add(rule.getRuleName());
                    rowData.add(rule.getRowDataSql());
                    sqlDataList.add(rowData);
                }
                
                // 写入SQL数据
                WriteTable sqlTable = new WriteTable();
                sqlTable.setHead(sqlHeadList);
                excelWriter.write(sqlDataList, sqlSheet, sqlTable);
                
                excelWriter.finish();
            } catch (Exception e) {
                log.error("数据写入excel时系统异常：", e);
            }

            // 生成压缩文件
            File attachmentFile = excelFile; // 默认使用Excel文件作为附件
            String zipFileName = rulesetValidateLog.getRulesetName() + "_" + date + ".zip";
            File zipFile = compressFile(excelFile, zipFileName);

            // 如果压缩成功，使用压缩文件作为附件；否则使用原Excel文件
            if (zipFile != null && zipFile.exists()) {
                attachmentFile = zipFile;
                log.info("Excel文件已压缩，将发送压缩文件作为附件：{}", zipFile.getName());
            } else {
                log.warn("文件压缩失败，将发送原Excel文件作为附件：{}", excelFile.getName());
            }

            // 给用户发送邮件
            String emailSubject = "【天才-数据质量管理系统】规则告警通知";
            // 根据附件类型调整邮件内容
            String attachmentNote = "(详细数据请见附件)";
            if (attachmentFile != null && attachmentFile.getName().endsWith(".zip")) {
                attachmentNote = "(详细数据请见压缩附件，解压后查看Excel文件)";
            }

            String emailContent = "<body><p style='margin: 0px;font-size:14px !important; font-family:microsoftyahei !important;'>\n" +
                    "<p style='font-size:14px !important; font-family:microsoftyahei !important;'>\n" +
                    "Dear华农人:\t</p>\n" +
                    "<p style='text-indent: 2em;font-size:14px !important; font-family:microsoftyahei !important;'>\n" +
                    "    " + message + "\t</p>\n" +
                    "<p style='text-indent: 2em;font-size:14px !important; font-family:microsoftyahei !important;'>\n" +
                    "    " + attachmentNote + "\t</p>\n" +
                    "</body>";
            String emailParamJson = EmailUtil.getEmailParamJson(emailSubject, emailContent, receivers);

            // 发送邮件，如果有附件则附加
           if (attachmentFile != null && attachmentFile.exists()) {
               MsgUtil.sendEmail(emailParamJson, attachmentFile);
           } else {
               MsgUtil.sendEmail(emailParamJson, null);
           }


            // 清理临时文件
            if (excelFile != null && excelFile.exists()) {
                excelFile.delete();
                log.debug("已删除临时Excel文件：{}", excelFile.getName());
            }
            if (zipFile != null && zipFile.exists()) {
                zipFile.delete();
                log.debug("已删除临时ZIP文件：{}", zipFile.getName());
            }


            log.info("发送邮件告警给{}，内容：{}，附件：{}", receivers, message, attachmentFile != null ? attachmentFile.getName() : "无");
        } catch (Exception e) {
            log.error("发送邮件告警失败", e);
            // 发送没有附件的邮件作为备份
            String emailSubject = "【天才-数据质量管理系统】规则告警通知";
            String emailContent = "<body><p style='margin: 0px;font-size:14px !important; font-family:microsoftyahei !important;'>\n" +
                    "<p style='font-size:14px !important; font-family:microsoftyahei !important;'>\n" +
                    "Dear华农人:\t</p>\n" +
                    "<p style='text-indent: 2em;font-size:14px !important; font-family:microsoftyahei !important;'>\n" +
                    "    " + message + "\t</p>\n" +
                    "<p style='text-indent: 2em;font-size:14px !important; font-family:microsoftyahei !important;'>\n" +
                    "    (附件生成失败，请查看系统获取详细数据)\t</p>\n" +
                    "</body>";
            String emailParamJson = EmailUtil.getEmailParamJson(emailSubject, emailContent, receivers);
            MsgUtil.sendEmail(emailParamJson, null);
        }
    }

    /**
     * 推送工单到Mantis系统
     * @param mantisIssueCreateDTO Mantis工单创建对象
     */
    private void sendMantisIssue(MantisIssueCreateDTO mantisIssueCreateDTO) {
        try {
            String apiUrl = mantisUrl + createIssues;
            log.info("开始推送工单到Mantis，URL: {}", apiUrl);
            
            // 验证Authorization配置
            if (StringUtils.isBlank(mantisAuthorization)) {
                log.warn("Mantis Authorization配置为空，可能导致认证失败");
            }
            
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS)
                    .build();

            String jsonString = JSON.toJSONString(mantisIssueCreateDTO);
            log.debug("Mantis工单请求参数: {}", jsonString);
            log.debug("使用Authorization配置进行认证");
            
            RequestBody requestBody = RequestBody.create(jsonString, MediaType.parse("application/json; charset=utf-8"));

            Request request = new Request.Builder()
                    .url(apiUrl)
                    .post(requestBody)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Authorization", mantisAuthorization)
                    .build();

            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body() != null ? response.body().string() : "";
                
                if (!response.isSuccessful()) {
                    log.error("推送工单到Mantis失败，状态码：{}，响应内容：{}", response.code(), responseBody);
                } else {
                    log.info("推送工单到Mantis成功，工单标题：{}，响应内容：{}", 
                            mantisIssueCreateDTO.getSummary(), responseBody);
                    
                    // 解析响应，获取工单ID（如果Mantis返回了工单ID的话）
                    try {
                        JSONObject responseJson = JSON.parseObject(responseBody);
                        if (responseJson.containsKey("issue")) {
                            JSONObject issue = responseJson.getJSONObject("issue");
                            if (issue.containsKey("id")) {
                                String issueId = issue.getString("id");
                                log.info("Mantis工单创建成功，工单ID: {}", issueId);
                            }
                        }
                    } catch (Exception parseEx) {
                        log.debug("解析Mantis响应失败，响应内容: {}", responseBody, parseEx);
                    }
                }
            }
        } catch (Exception e) {
            log.error("推送工单到Mantis失败，工单标题: {}", mantisIssueCreateDTO.getSummary(), e);
        }
    }

}
