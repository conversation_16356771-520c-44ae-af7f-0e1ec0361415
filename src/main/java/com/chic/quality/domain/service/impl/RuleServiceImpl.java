package com.chic.quality.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chic.commons.exception.ApiException;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.quality.apis.model.dto.RuleDTO;
import com.chic.quality.apis.model.dto.RuleExecuteContext;
import com.chic.quality.apis.model.dto.RuleExecutionRequest;
import com.chic.quality.apis.model.vo.QueryRuleVo;
import com.chic.quality.apis.model.vo.RulesetVo;
import com.chic.quality.apis.model.vo.SaveRuleVo;
import com.chic.quality.apis.model.vo.UpdateRuleVo;
import com.chic.quality.apis.protocol.mapping.RuleMapping;
import com.chic.quality.apis.protocol.mapping.RulesetMapping;
import com.chic.quality.domain.database.entity.*;
import com.chic.quality.domain.database.mapper.RuleMapper;
import com.chic.quality.domain.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chic.quality.infrastructure.general.constants.TaskExecutionStatus;
import com.chic.quality.infrastructure.general.util.MybatisApiUtils;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import com.chic.commons.base.PageData;
import org.springframework.util.CollectionUtils;

/**
 * <p>
 * 质量规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Slf4j
@Service
public class RuleServiceImpl extends ServiceImpl<RuleMapper, Rule> implements RuleService {
    private static final String PARTITION_EXPRESSION_FROM_SCHEDULE = "SCHEDULE";
    private static final String PARTITION_EXPRESSION_FROM_CUSTOM = "CUSTOM";
    private static final String PARTITION_TYPE_NONE_PARTITIONS = "NONE_PARTITIONS";
    private static final String PARTITION_TYPE_BIZ_DATE = "BIZ_DATE";
    private static final String PARTITION_TYPE_EXECUTE_TIME = "EXECUTE_TIME";
    private static final String SCHEDULE_TYPE_PERIOD_SCHEDULE = "PERIOD_SCHEDULE";


    @Autowired
    private RuleMapping ruleMapping;
    @Autowired
    private RuleScheduleLinkService ruleScheduleLinkService;
    @Autowired
    private RuleScheduleInfoService ruleScheduleInfoService;
    @Autowired
    private RuleExecuteService ruleExecuteService;
    @Autowired
    private RuleMapper ruleMapper;
    @Autowired
    private RulesetValidateLogService rulesetValidateLogService;
    @Autowired
    private RuleAlertConfigService ruleAlertConfigService;

    

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(SaveRuleVo saveRuleVo) {
        // 保存基本信息
        this.check(saveRuleVo);

        Rule po = ruleMapping.toPo(saveRuleVo);
        this.save(po);

        this.saveScheduleLink(saveRuleVo.getRuleScheduleIds(), po);

        return po.getId();
    }

    @Override
    public QueryRuleVo selectDetailById(Long id) {
        Rule rule = getById(id);
        QueryRuleVo query = ruleMapping.toQuery(rule);
        //query.setValidateObjects(ruleValidateObjectService.listByRuleId(id));
        //query.setRuleAlarmConfigs(ruleAlarmConfigService.listByRuleId(id));
        query.setRuleScheduleLinks(ruleScheduleLinkService.selectVoByRuleId(id));

        return query;
    }

    @Override
    public Boolean trialRun(RuleExecutionRequest request) {
        RuleScheduleInfo ruleSchedule;
        if(PARTITION_EXPRESSION_FROM_SCHEDULE.equals(request.getPartitionExpressionFrom())) {
            ruleSchedule = ruleScheduleInfoService.getById(request.getScheduleId());
            if (ruleSchedule == null) {
                throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "未找到调度信息"));
            }
        } else {
            ruleSchedule = new RuleScheduleInfo(-1L, -1,"自定义调度" ,SCHEDULE_TYPE_PERIOD_SCHEDULE, 
                                               PARTITION_TYPE_BIZ_DATE, request.getPartitionExpression());
        }
        RulesetVo rulesetVo = request.getRulesetVo();
        RulesetValidateLog rulesetValidateLog = rulesetValidateLogService.insert(ruleSchedule,rulesetVo);
        
        List<Long> ruleIds = request.getRuleIds();
        // 先将所有规则状态更新为执行中
        for (Long ruleId : ruleIds) {
            updateTrialStatus(ruleId, 3); // 3表示执行中
        }
        List<RuleAlertConfig> alertConfigs = ruleAlertConfigService.listByRulesetId(rulesetVo.getId());
        final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        // 异步执行规则试跑
        CompletableFuture.runAsync(() -> {
            // 在异步线程中设置安全上下文
            if (authentication != null) {
                SecurityContextHolder.getContext().setAuthentication(authentication);
            }
            Integer triggerRuleCount = 0;
            try {
                List<RuleDTO> failedRules = new ArrayList<>();
                for (Long ruleId : ruleIds) {
                    try {
                        Rule rule = getById(ruleId);
                        RuleDTO ruleDTO = ruleMapping.toRuleDTO(rule);
                        
                        RuleExecuteContext ruleExecuteContext = new RuleExecuteContext(ruleDTO, rulesetVo, ruleSchedule);
                        ruleExecuteContext.setBatchNumber(rulesetValidateLog.getBatchNumber());
                        ruleExecuteContext.setBizDate(request.getBizDate());
                        ruleExecuteContext.setTryRun(true);
                        boolean executeResult = ruleExecuteService.executeRule(ruleExecuteContext);
                        // 更新试跑结果
                        updateTrialStatus(ruleId, executeResult ? 1 : 2);
                        
                        if(executeResult && ruleExecuteContext.getValidateStatus()){
                            triggerRuleCount++;
                            failedRules.add(ruleDTO);
                        }
                        log.info("规则试跑完成 ruleId={}, result={}", ruleId, executeResult);
                    } catch (Exception e) {
                        log.error("规则试跑异常 ruleId={}", ruleId, e);
                        updateTrialStatus(ruleId, 2);
                    }
                }
                if (triggerRuleCount > 0 && !alertConfigs.isEmpty()) {
                    log.info("规则集触发告警，rulesetId:{},scheduleId:{},triggerRuleCount:{}", rulesetVo.getId(), ruleSchedule.getId(), triggerRuleCount);
                    ruleAlertConfigService.handleAlerts(alertConfigs, failedRules, rulesetVo, rulesetValidateLog);
                }
                rulesetValidateLogService.update(rulesetValidateLog.getId(), TaskExecutionStatus.SUCCESS.name(), triggerRuleCount);
            } catch (Exception e) {
                log.error("规则试跑整体异常", e);
                // 将所有规则状态更新为失败
                for (Long ruleId : ruleIds) {
                    updateTrialStatus(ruleId, 2);
                }
                rulesetValidateLogService.update(rulesetValidateLog.getId(), TaskExecutionStatus.FAILED.name(), triggerRuleCount);
            }
            
        });
        return true;
    }

    
    private void updateTrialStatus(Long ruleId, int status) {
        LambdaUpdateWrapper<Rule> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Rule::getTrialResult, status); // 3表示执行中
        updateWrapper.eq(Rule::getId, ruleId);
        this.update(updateWrapper);
    }

   
    private void check(SaveRuleVo saveRuleVo) {
        this.checkRuleName(saveRuleVo.getRuleName());
        //this.checkValidateObject(saveRuleVo.getValidateObjects());
    }


    private void checkRuleName(String ruleName) {
        LambdaQueryWrapper<Rule> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Rule::getRuleName, ruleName);
        if (this.count(wrapper)>0) {
            throw new ApiException(new ErrorResult(ErrorResultCode.REQUEST_BODY_REPEAT.getErrorCode(), "规则名称已存在"));
        }
    }
    private void checkRuleName(Long id,String ruleName) {
        LambdaQueryWrapper<Rule> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Rule::getRuleName, ruleName);
        wrapper.ne(Rule::getId, id);
        if (this.count(wrapper)>0) {
            throw new ApiException(new ErrorResult(ErrorResultCode.REQUEST_BODY_REPEAT.getErrorCode(), "规则名称已存在"));
        }
    }

    @Override
    public List<Rule> selectByRulesetId(Long rulesetId) {
        LambdaQueryWrapper<Rule> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Rule::getRulesetId, rulesetId);
        return this.list(wrapper);
    }

    @Override
    public PageData<QueryRuleVo> selectByPage(Long rulesetId,String ruleName) {
        IPage pageParam = MybatisApiUtils.getPageParam();
        IPage<QueryRuleVo> pages = ruleMapper.selectByPage(pageParam, rulesetId,ruleName);
        for (QueryRuleVo queryRuleVo : pages.getRecords()) {
            queryRuleVo.setRuleScheduleLinks(ruleScheduleLinkService.selectVoByRuleId(queryRuleVo.getId()));
        }
        return PageData.convert(pages);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateValidFlag(Long id, Boolean valid) {
        LambdaUpdateWrapper<Rule> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Rule::getEnable, valid);
        updateWrapper.eq(Rule::getId, id);
        return update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id) {
        ruleScheduleLinkService.removeByRuleId(id);
        return removeById(id);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(UpdateRuleVo updateRuleVo) {
        this.checkRuleName(updateRuleVo.getId(),updateRuleVo.getRuleName());
        Rule po = ruleMapping.toRule(updateRuleVo);

        if(Collections.isEmpty(updateRuleVo.getRuleScheduleIds())){
            ruleScheduleLinkService.removeByRuleId(po.getId());
        }else{
            ruleScheduleLinkService.removeByRuleId(po.getId());
            saveScheduleLink(updateRuleVo.getRuleScheduleIds(), po);
        }
        return updateById(po);
    }

    private void saveScheduleLink(List<Long> ruleScheduleIds, Rule po) {
        if (CollectionUtils.isEmpty(ruleScheduleIds)) {
            return;
        }
        List<RuleScheduleLink> ruleScheduleLinks = new ArrayList<>();
        for (Long ruleScheduleId : ruleScheduleIds) {
            RuleScheduleLink ruleScheduleLink = new RuleScheduleLink();
            ruleScheduleLink.setRuleId(po.getId());
            ruleScheduleLink.setRulesetId(po.getRulesetId());
            ruleScheduleLink.setScheduleId(ruleScheduleId);
            ruleScheduleLinks.add(ruleScheduleLink);
        }
        ruleScheduleLinkService.saveBatch(ruleScheduleLinks);
    }
    
    @Override
    public List<Rule> listByRulesetId(Long rulesetId) {
        LambdaQueryWrapper<Rule> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Rule::getRulesetId, rulesetId);
        return ruleMapper.selectList(wrapper);
    }
}
