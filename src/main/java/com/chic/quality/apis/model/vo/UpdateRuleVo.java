package com.chic.quality.apis.model.vo;

import com.chic.quality.domain.database.entity.RuleAlarmConfig;
import com.chic.quality.domain.database.entity.RuleScheduleLink;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 质量规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UpdateRuleVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 规则ID
     */
    @NotNull(message = "规则ID不能为空")
    private Long id;
    /**
     * 规则集ID
     */
    @NotNull(message = "规则集ID不能为空")
    private Long rulesetId;
    /**
     * 模版ID
     */
    @NotNull(message = "模版ID不能为空")
    private Long templateId;
    /**
     * 规则类型
     * 1: 一致性
     * 2: 有效性
     * 3: 及时性
     * 4: 唯一性
     * 5: 完整性
     * 6: 稳定性
     */
    @NotNull(message = "规则类型不能为空")
    private Integer ruleType;
    /**
     * 规则名称
     */
    @NotNull(message = "规则名称不能为空")
    private String ruleName;

    /**
     * 规则强度 WEAK:弱 STRONG:强
     */
    @NotNull(message = "规则强度不能为空")
    private String ruleStrength;
    /**
     * 是否开启
     */
    private Boolean enable;

    /**
     * 过滤条件
     */
    private String filter;

    /**
     * 校验配置
     */
    @NotNull(message = "校验条件不能为空")
    private String validateCondition;
    /**
     * 告警配置
     */
    //private List<RuleAlarmConfig> ruleAlarmConfigs;
    /**
     * 调度配置
     */
    private List<Long> ruleScheduleIds;
    /**
     * 监控对象ID(例如数据集ID或图表ID)
     */
    @NotNull(message = "监控对象ID不能为空")
    private Long watchId;
    /**
     * 校验对象集合
     */
    @NotNull(message = "校验对象不能为空")
    private String validateObjects;
    /**
     * 比较对象
     */
    private String compareObject;
    /**
     * 监控类型 VIEW:视图，TABLE:表,默认VIEW
     */
    private String watchType;
    /**
     * 备注
     */
    private String remark;
    /**
     * 异常归档
     */
    @NotNull(message = "异常归档不能为空")
    private boolean exceptionArchive;
    /**
     * 归档模式 1-仅归档异常字段 2-归档完整记录,默认1
     */
    private int archiveMode;
    /**
     * 记分方式 1-质量校验状态 2-数据合格比例
     * 质量校验状态：按照当前规则最近一次执行成功的校验记录校验状态进行打分，校验通过100分，校验不通过0分
     * 合格数据比例：将当前规则最近一次执行成功的校验记录中的正常数据的比例（即正常率）作为分数，如数据格式有效性是80%，那么质量分就是80分
     */
    private String scoreType;
    /**
     * 质量分数(1-10)
     */
    private int qualityScore;
    /**
     * 配置方式（1-模版创建 2-自定义-SQL）
     */
    @NotNull(message = "配置方式不能为空")
    private String configMethod;
    /**
     * 对比数据集ID
     */
    private Long compareDataSetId;

    /**
     * 阀值操作符（>、>=）
     */
    private String thresholdOperator;

    /**
     * 阀值
     */
    private Double thresholdValue;

    /**
     * Mantis项目ID
     */
    private Long mantisProjectId;

    /**
     * Mantis处理人ID
     */
    private Long mantisHandlerId;

}
