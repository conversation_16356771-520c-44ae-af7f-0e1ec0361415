package com.chic.quality.apis.model.vo;

import com.chic.quality.domain.database.entity.BaseEntity;
import com.chic.quality.domain.database.entity.RuleAlarmConfig;
import com.chic.quality.domain.database.entity.RuleScheduleLink;
import com.chic.quality.domain.database.entity.RuleValidateObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 质量规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QueryRuleVo extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 规则ID
     */
    private Long id;
    /**
     * 规则集ID
     */
    private Long rulesetId;
    /**
     * 模版ID
     */
    private Long templateId;
    /**
     * 模版名称
     */
    private String templateName;
    /**
     * 数据集ID
     */
    private Long dataSetId;
    /**
     * 数据集名称
     */
    private String dataSetName;
    /**
     * 规则类型
     * 1: 一致性
     * 2: 有效性
     * 3: 及时性
     * 4: 唯一性
     * 5: 完整性
     * 6: 稳定性
     */
    private Integer ruleType;
    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则强度
     */
    private String ruleStrength;

    /**
     * 序号
     */
    private Integer ruleNo;

    /**
     * 是否开启
     */
    private Boolean enable;

    /**
     * 过滤条件
     */
    private String filter;
    /**
     * 创建时间
     */
    private String createAt;

    /**
     * 更新时间
     */
    private String updateAt;

    /**
     * 校验对象
     */
    private String validateObjects;

    /**
     * 比较对象
     */
    private String compareObject;

    /**
     * 告警配置
     */
    private List<RuleAlarmConfig> ruleAlarmConfigs;
    /**
     * 调度配置
     */
    private List<RuleScheduleLinkVo> ruleScheduleLinks;
    /**
     * 试跑结果
     * 0: 未试跑
     * 1: 试跑成功
     * 2: 试跑失败
     */
    private int trialResult;
    /**
     * 监控类型 VIEW:视图，TABLE:表,默认VIEW
     */
    private String watchType;
    /**
     * 备注
     */
    private String remark;
    /**
     * 异常归档
     */
    private boolean exceptionArchive;
    /**
     * 归档模式 1-仅归档异常字段 2-归档完整记录,默认1
     */
    private int archiveMode;
    /**
     * 记分方式 1-质量校验状态 2-数据合格比例
     * 质量校验状态：按照当前规则最近一次执行成功的校验记录校验状态进行打分，校验通过100分，校验不通过0分
     * 合格数据比例：将当前规则最近一次执行成功的校验记录中的正常数据的比例（即正常率）作为分数，如数据格式有效性是80%，那么质量分就是80分
     */
    private int scoreType;
    /**
     * 质量分数(1-10)
     */
    private int qualityScore;
    /**
     * 配置方式（1-模版创建 2-自定义-SQL）
     */
    private int configMethod;
    /**
     * 校验条件
     *
     {
     "metric": "NORMAL_NUMBER",
     "operator": ">",
     "value":0,
     "unit": null
     }
     */
    private String validateCondition;

    private Long watchId;

    /**
     * 阀值操作符（>、>=）
     */
    private String thresholdOperator;

    /**
     * 阀值
     */
    private Double thresholdValue;

    /**
     * Mantis项目ID
     */
    private Long mantisProjectId;

    /**
     * Mantis处理人ID
     */
    private Long mantisHandlerId;

}
