package com.chic.quality.apis.controller;

import com.chic.commons.base.Result;
import com.chic.quality.apis.model.dto.MantisProjectDTO;
import com.chic.quality.apis.model.dto.MantisUserDTO;
import com.chic.quality.domain.service.MantisDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * Mantis数据查询接口
 * 
 * <AUTHOR>
 * @since 2025-06-25
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/df/api/mantis")
public class MantisDataController {

    @Autowired
    private MantisDataService mantisDataService;

    /**
     * 查询Mantis系统中的所有用户信息
     * 
     * @return 用户列表，包含id和realname(name)字段
     */
    @GetMapping("/users")
    public Result<List<MantisUserDTO>> getAllUsers() {
        log.info("开始查询Mantis用户信息");
        try {
            List<MantisUserDTO> users = mantisDataService.getUserList();
            log.info("查询Mantis用户信息成功，共{}个用户", users.size());
            return Result.success(users);
        } catch (Exception e) {
            log.error("查询Mantis用户信息失败", e);
            throw e;
        }
    }

    /**
     * 查询Mantis系统中的所有项目信息
     * 
     * @return 项目列表，包含id和name字段
     */
    @GetMapping("/projects")
    public Result<List<MantisProjectDTO>> getAllProjects() {
        log.info("开始查询Mantis项目信息");
        try {
            List<MantisProjectDTO> projects = mantisDataService.getProjectList();
            log.info("查询Mantis项目信息成功，共{}个项目", projects.size());
            return Result.success(projects);
        } catch (Exception e) {
            log.error("查询Mantis项目信息失败", e);
            throw e;
        }
    }

    
} 